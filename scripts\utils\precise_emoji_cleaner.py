#!/usr/bin/env python3
"""
精确清理代码中的emoji表情符号
只清理真正的emoji，不影响中文字符
"""

import os
import re
from pathlib import Path

class PreciseEmojiCleaner:
    """精确Emoji清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.cleaned_files = []
        self.total_emojis_removed = 0
        
        # 精确的emoji替换映射 - 只包含真正的emoji字符
        self.emoji_replacements = {
            # 状态类emoji
            '🚨': '[紧急]',
            '🛑': '[停止]', 
            '✅': '[成功]',
            '❌': '[错误]',
            '⚠️': '[警告]',
            '⚠': '[警告]',
            'ℹ️': '[信息]',
            'ℹ': '[信息]',
            
            # 动作类emoji
            '🚀': '[启动]',
            '🔍': '[检查]',
            '🔧': '[修复]',
            '🧪': '[测试]',
            '📦': '[包]',
            '📝': '[记录]',
            '📊': '[统计]',
            '📈': '[图表]',
            
            # 时间类emoji
            '⏳': '[等待]',
            '⏰': '[时间]',
            
            # 庆祝类emoji
            '🎉': '[完成]',
            '🎊': '[庆祝]',
            '👍': '[好]',
            '👎': '[差]',
            
            # 其他常见emoji
            '💡': '[想法]',
            '🔥': '[热门]',
            '💯': '[百分百]',
            '🌟': '[星级]',
            '⭐': '[星]',
            '🔑': '[关键]',
            '🎯': '[目标]',
            '📋': '[清单]',
            '📌': '[标记]',
            '🔗': '[链接]',
            '📁': '[文件夹]',
            '📄': '[文档]',
            '🖥️': '[电脑]',
            '🖥': '[电脑]',
            '💻': '[笔记本]',
            '📱': '[手机]',
            '🌐': '[网络]',
            '🔒': '[锁定]',
            '🔓': '[解锁]',
            '🛠️': '[工具]',
            '🛠': '[工具]',
            '⚙️': '[设置]',
            '⚙': '[设置]',
            '🔄': '[刷新]',
            '🔃': '[循环]',
            '🔁': '[重复]',
            '▶️': '[播放]',
            '▶': '[播放]',
            '⏸️': '[暂停]',
            '⏸': '[暂停]',
            '⏹️': '[停止]',
            '⏹': '[停止]',
            '⏭️': '[下一个]',
            '⏭': '[下一个]',
            '⏮️': '[上一个]',
            '⏮': '[上一个]',
        }
    
    def clean_emoji_in_text(self, text: str) -> tuple:
        """清理文本中的emoji"""
        original_text = text
        emoji_count = 0
        
        # 只替换已知的emoji
        for emoji, replacement in self.emoji_replacements.items():
            if emoji in text:
                count = text.count(emoji)
                text = text.replace(emoji, replacement)
                emoji_count += count
        
        return text, emoji_count
    
    def clean_file(self, file_path: Path) -> bool:
        """清理单个文件"""
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 清理emoji
            cleaned_content, emoji_count = self.clean_emoji_in_text(content)
            
            # 如果有变化，写回文件
            if emoji_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                self.cleaned_files.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'emoji_count': emoji_count
                })
                self.total_emojis_removed += emoji_count
                print(f"清理 {file_path.relative_to(self.project_root)}: {emoji_count} 个emoji")
                return True
            
            return False
            
        except Exception as e:
            print(f"清理文件失败 {file_path}: {e}")
            return False
    
    def restore_damaged_files(self):
        """恢复被错误清理的文件"""
        print("恢复被错误清理的文件...")
        
        # 需要恢复的文件模式
        damaged_patterns = [
            '[符号]',
            '[[符号][符号]]',
            '[[符号][符号][符号]]'
        ]
        
        for pattern in ['*.py', '*.sh']:
            for file_path in self.project_root.rglob(pattern):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否包含损坏的模式
                    if any(damaged in content for damaged in damaged_patterns):
                        print(f"发现损坏文件: {file_path.relative_to(self.project_root)}")
                        # 这里可以添加恢复逻辑，比如从备份恢复
                        
                except Exception as e:
                    print(f"检查文件失败 {file_path}: {e}")
    
    def clean_project(self):
        """清理整个项目"""
        print("开始精确清理项目中的emoji表情...")
        print("=" * 60)
        
        # 首先恢复被错误清理的文件
        self.restore_damaged_files()
        
        # 要清理的文件类型
        file_patterns = ['*.py', '*.sh']
        
        # 排除的目录
        excluded_dirs = {'__pycache__', '.git', 'node_modules', 'venv', 'env'}
        
        cleaned_count = 0
        total_files = 0
        
        for pattern in file_patterns:
            for file_path in self.project_root.rglob(pattern):
                # 跳过排除的目录
                if any(excluded_dir in file_path.parts for excluded_dir in excluded_dirs):
                    continue

                # 跳过emoji清理脚本自己
                if file_path.name in ['clean_emoji_violations.py', 'precise_emoji_cleaner.py']:
                    print(f"跳过emoji清理脚本: {file_path.relative_to(self.project_root)}")
                    continue

                # 跳过已经损坏的文件
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    if '[符号]' in content:
                        print(f"跳过损坏文件: {file_path.relative_to(self.project_root)}")
                        continue
                except:
                    continue
                
                total_files += 1
                if self.clean_file(file_path):
                    cleaned_count += 1
        
        print("\n" + "=" * 60)
        print("精确清理完成统计:")
        print(f"总文件数: {total_files}")
        print(f"清理文件数: {cleaned_count}")
        print(f"移除emoji总数: {self.total_emojis_removed}")
        
        if self.cleaned_files:
            print("\n清理详情:")
            for file_info in self.cleaned_files:
                print(f"  {file_info['file']}: {file_info['emoji_count']} 个emoji")

def main():
    """主函数"""
    cleaner = PreciseEmojiCleaner()
    cleaner.clean_project()

if __name__ == "__main__":
    main()
